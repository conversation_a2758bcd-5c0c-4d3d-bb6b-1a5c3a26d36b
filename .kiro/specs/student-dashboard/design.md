# Design Document

## Overview

The Student Dashboard is a comprehensive, centralized hub that serves as the primary landing page for authenticated users. It provides a modern, aesthetic interface combining personalized learning analytics, task management, performance tracking, and self-assessment tools. The dashboard follows the existing application's design system using Tailwind CSS, shadcn/ui components, and maintains consistency with the current dark/light theme implementation.

The dashboard will be implemented as a new React component that integrates seamlessly with the existing routing structure, authentication system, and data stores. It leverages the current Supabase backend for data persistence and real-time updates.

## Architecture

### Component Hierarchy

```
StudentDashboard (Main Container)
├── DashboardHeader (Enhanced header with notifications, search, theme toggle)
├── CollapsibleSidebar (Navigation with icons and labels)
├── DashboardGrid (Main content area with 2x3 responsive grid)
│   ├── Row 1 (Top Row)
│   │   ├── TodaysTasksWidget (Top-left: Task management with comprehensive details)
│   │   └── UpcomingExamsWidget (Top-right: Exam countdown with syllabus tracking)
│   ├── Row 2 (Middle Row)
│   │   ├── SWOTAnalysisWidget (Middle-left: AI-powered self-assessment)
│   │   └── PrepInsightBoardWidget (Middle-right: Study metrics and streak insights)
│   └── Row 3 (Bottom Row)
│       ├── AnalyticsOverviewWidget (Bottom-left: Performance metrics and trends)
│       └── DDayCountdownWidget (Bottom-right: Final countdown timer)
└── DashboardFooter (Optional footer with quick links)
```

### Data Flow Architecture

```mermaid
graph TD
    A[Student Dashboard] --> B[Supabase Auth Context]
    A --> C[Dashboard Store]
    C --> D[Tasks Store]
    C --> E[Analytics Store]
    C --> F[User Profile Store]
    C --> G[Subjects Store]
    
    D --> H[Supabase Tasks API]
    E --> I[Supabase Analytics API]
    F --> J[Supabase User API]
    G --> K[Supabase Subjects API]
    
    L[Real-time Updates] --> C
    M[AI Service] --> N[SWOT Analysis Widget]
```

### State Management

The dashboard will use a combination of:
- **Zustand stores** for global state management (following existing patterns)
- **React Query** for server state and caching
- **Local state** for UI-specific interactions
- **Context providers** for theme and authentication

## Components and Interfaces

### Core Dashboard Component

```typescript
interface StudentDashboardProps {
  userId: string;
  userProfile: UserProfile;
  initialLayout?: DashboardLayout;
}

interface DashboardLayout {
  widgets: WidgetConfig[];
  gridColumns: number;
  customizations: LayoutCustomization;
}

interface WidgetConfig {
  id: string;
  type: WidgetType;
  position: GridPosition;
  size: WidgetSize;
  isVisible: boolean;
  customProps?: Record<string, any>;
}
```

### Enhanced Header Component

```typescript
interface DashboardHeaderProps {
  user: User;
  notifications: Notification[];
  onSearch: (query: string) => void;
  onThemeToggle: () => void;
  onNotificationClick: (notification: Notification) => void;
}

interface Notification {
  id: string;
  type: 'exam' | 'task' | 'achievement' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
}
```

### Collapsible Sidebar Component

```typescript
interface CollapsibleSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  activeSection: string;
  navigationItems: NavigationItem[];
}

interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType;
  path: string;
  badge?: number;
  isActive: boolean;
}
```

### Widget Components

#### Today's Tasks Widget

```typescript
interface TodaysTasksWidgetProps {
  tasks: Task[];
  onTaskComplete: (taskId: string) => void;
  onTaskAdd: (task: Partial<Task>) => void;
  onTaskReorder: (tasks: Task[]) => void;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'high' | 'medium' | 'low';
  dueTime?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  category: string;
  isCompleted: boolean;
  isRecurring: boolean;
}
```

#### Upcoming Exams Widget

```typescript
interface UpcomingExamsWidgetProps {
  exams: Exam[];
  onExamClick: (exam: Exam) => void;
}

interface Exam {
  id: string;
  name: string;
  subject: string;
  date: Date;
  time: string;
  location?: string;
  instructor?: string;
  preparationProgress: number;
  studyMaterials: StudyMaterial[];
  countdownDays: number;
  isUrgent: boolean;
}
```

#### Chapter Progress Widget

```typescript
interface ChapterProgressWidgetProps {
  subjects: Subject[];
  chapters: Chapter[];
  onChapterClick: (chapter: Chapter) => void;
}

interface Chapter {
  id: string;
  title: string;
  subject: string;
  completionPercentage: number;
  difficultyLevel: 'easy' | 'medium' | 'hard';
  estimatedTime: number;
  hasNotes: boolean;
  hasQuiz: boolean;
  lastAccessed?: Date;
}
```

#### AI-Powered SWOT Analysis Widget

```typescript
interface SWOTAnalysisWidgetProps {
  currentAnalysis: SWOTAnalysis;
  onAnalysisUpdate: (analysis: Partial<SWOTAnalysis>) => void;
  onAIAnalyze: () => Promise<AIInsights>;
}

interface SWOTAnalysis {
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
  lastUpdated: Date;
  aiInsights?: AIInsights;
}

interface AIInsights {
  studyMethodsAnalysis: string;
  gritAssessment: string;
  recommendations: string[];
  improvementAreas: string[];
}
```

#### Productivity Leaderboard Widget

```typescript
interface ProductivityLeaderboardWidgetProps {
  leaderboard: LeaderboardEntry[];
  currentUser: LeaderboardEntry;
  onViewDetails: () => void;
}

interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  rank: number;
  points: number;
  studyTime: number;
  tasksCompleted: number;
  achievements: Achievement[];
  isCurrentUser: boolean;
}
```

#### Prep Insight Board Widget

```typescript
interface PrepInsightBoardWidgetProps {
  studyMetrics: StudyInsightMetrics;
  streakData: StreakData;
  personalRecords: PersonalRecords;
  onMetricClick: (metric: string) => void;
  onDrillDown: (section: string) => void;
}

interface StudyInsightMetrics {
  averageDailyStudyTime: number; // in minutes
  todayStudyTime: number;
  weeklyTrend: number; // percentage change
  monthlyTrend: number;
  consistencyScore: number; // 0-100
}

interface StreakData {
  currentStreak: number;
  longestStreak: number;
  streakHistory: StreakDay[];
  milestones: Milestone[];
  nextMilestone: Milestone;
}

interface PersonalRecords {
  highestStudyHours: {
    value: number;
    date: Date;
    subject?: string;
  };
  longestSession: {
    duration: number;
    date: Date;
    subject?: string;
  };
  mostProductiveDay: {
    date: Date;
    studyTime: number;
    tasksCompleted: number;
  };
}

interface StreakDay {
  date: Date;
  studyTime: number;
  isStreakDay: boolean;
}

interface Milestone {
  days: number;
  title: string;
  badge: string;
  isAchieved: boolean;
}
```

## Data Models

### Dashboard Configuration

```typescript
interface DashboardConfig {
  id: string;
  userId: string;
  layout: DashboardLayout;
  preferences: DashboardPreferences;
  createdAt: Date;
  updatedAt: Date;
}

interface DashboardPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultView: string;
  widgetSettings: Record<string, any>;
  notifications: NotificationSettings;
}
```

### Analytics Data Models

```typescript
interface DashboardAnalytics {
  studyTime: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    trend: number;
  };
  taskCompletion: {
    completed: number;
    total: number;
    completionRate: number;
  };
  examPreparation: {
    upcomingCount: number;
    averagePreparation: number;
    urgentCount: number;
  };
  subjectProgress: {
    totalChapters: number;
    completedChapters: number;
    averageProgress: number;
  };
}
```

## Error Handling

### Error Boundaries

```typescript
interface DashboardErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class DashboardErrorBoundary extends Component<
  PropsWithChildren<{}>,
  DashboardErrorBoundaryState
> {
  // Error boundary implementation for graceful error handling
}
```

### Error States

- **Network Errors**: Show retry mechanisms with offline indicators
- **Data Loading Errors**: Display skeleton states with error messages
- **Widget Errors**: Isolate errors to individual widgets without breaking the entire dashboard
- **Authentication Errors**: Redirect to login with appropriate messaging

### Fallback Components

```typescript
interface ErrorFallbackProps {
  error: Error;
  resetError: () => void;
  widgetType?: string;
}

const WidgetErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetError,
  widgetType
}) => {
  // Render appropriate error UI based on widget type
};
```

## Testing Strategy

### Unit Testing

- **Component Testing**: Test individual widgets and their interactions
- **Hook Testing**: Test custom hooks for data fetching and state management
- **Utility Testing**: Test helper functions and data transformations

### Integration Testing

- **Dashboard Flow**: Test complete user workflows from login to dashboard interaction
- **API Integration**: Test data fetching and real-time updates
- **Theme Integration**: Test light/dark mode switching

### E2E Testing

- **User Journeys**: Test complete user scenarios including customization
- **Performance Testing**: Test dashboard loading times and responsiveness
- **Accessibility Testing**: Test keyboard navigation and screen reader compatibility

### Testing Tools

```typescript
// Jest + React Testing Library for unit tests
describe('StudentDashboard', () => {
  it('should render all widgets when user is authenticated', () => {
    // Test implementation
  });
});

// Cypress for E2E testing
describe('Dashboard User Journey', () => {
  it('should allow user to customize dashboard layout', () => {
    // E2E test implementation
  });
});
```

## Performance Considerations

### Optimization Strategies

1. **Lazy Loading**: Load widgets on-demand based on viewport visibility
2. **Memoization**: Use React.memo and useMemo for expensive calculations
3. **Virtual Scrolling**: Implement for large lists in widgets
4. **Code Splitting**: Split dashboard code into separate chunks
5. **Caching**: Implement intelligent caching for frequently accessed data

### Performance Monitoring

```typescript
interface PerformanceMetrics {
  dashboardLoadTime: number;
  widgetRenderTimes: Record<string, number>;
  apiResponseTimes: Record<string, number>;
  memoryUsage: number;
}

const usePerformanceMonitoring = () => {
  // Performance monitoring hook implementation
};
```

### Bundle Size Optimization

- **Tree Shaking**: Remove unused code from bundles
- **Dynamic Imports**: Load components only when needed
- **Asset Optimization**: Optimize images and icons
- **Dependency Analysis**: Regular audit of package dependencies

## Accessibility Features

### WCAG 2.1 AA Compliance

1. **Keyboard Navigation**: Full keyboard accessibility for all interactive elements
2. **Screen Reader Support**: Proper ARIA labels and descriptions
3. **Color Contrast**: Ensure sufficient contrast ratios in both themes
4. **Focus Management**: Clear focus indicators and logical tab order
5. **Alternative Text**: Descriptive alt text for all images and icons

### Accessibility Implementation

```typescript
interface AccessibilityProps {
  ariaLabel?: string;
  ariaDescribedBy?: string;
  role?: string;
  tabIndex?: number;
}

const AccessibleWidget: React.FC<AccessibilityProps> = ({
  ariaLabel,
  ariaDescribedBy,
  role,
  tabIndex,
  children
}) => {
  // Accessible widget wrapper implementation
};
```

### Inclusive Design Features

- **High Contrast Mode**: Enhanced contrast for users with visual impairments
- **Font Size Scaling**: Support for user-defined font size preferences
- **Reduced Motion**: Respect user's motion preferences
- **Language Support**: Internationalization ready structure

## Security Considerations

### Data Protection

1. **Input Validation**: Sanitize all user inputs
2. **XSS Prevention**: Implement Content Security Policy
3. **CSRF Protection**: Use CSRF tokens for state-changing operations
4. **Data Encryption**: Encrypt sensitive data in transit and at rest

### Authentication & Authorization

```typescript
interface SecurityContext {
  user: User;
  permissions: Permission[];
  sessionToken: string;
  isAuthenticated: boolean;
}

const useSecurityContext = () => {
  // Security context hook implementation
};
```

### Privacy Features

- **Data Minimization**: Only collect necessary user data
- **Consent Management**: Clear consent mechanisms for data usage
- **Data Export**: Allow users to export their data
- **Data Deletion**: Provide mechanisms for data deletion

## Integration Points

### Existing System Integration

1. **Authentication**: Integrate with existing Supabase auth system
2. **Routing**: Use existing React Router setup
3. **Theme System**: Leverage existing dark/light theme implementation
4. **Component Library**: Use existing shadcn/ui components
5. **State Management**: Integrate with existing Zustand stores

### API Integration

```typescript
interface DashboardAPI {
  getDashboardConfig: (userId: string) => Promise<DashboardConfig>;
  updateDashboardLayout: (userId: string, layout: DashboardLayout) => Promise<void>;
  getWidgetData: (widgetType: string, userId: string) => Promise<any>;
  updateWidgetData: (widgetType: string, userId: string, data: any) => Promise<void>;
}
```

### Real-time Features

- **WebSocket Integration**: Real-time updates for leaderboard and notifications
- **Optimistic Updates**: Immediate UI updates with server reconciliation
- **Conflict Resolution**: Handle concurrent updates gracefully

## UI Design System

### Design Philosophy

The Student Dashboard follows a **modern minimalist aesthetic** with clean lines, purposeful spacing, and subtle visual hierarchy. The design emphasizes:

- **Clarity over complexity**: Clean, uncluttered interfaces
- **Purposeful color usage**: Strategic use of color to convey meaning and create visual interest
- **Consistent iconography**: Varied, meaningful icons that enhance usability
- **Subtle depth**: Minimal use of shadows and gradients for depth perception
- **Responsive elegance**: Beautiful across all device sizes

### Color Palette & Icon Strategy

#### Primary Color System
```css
:root {
  /* Primary Brand Colors */
  --primary-blue: #3b82f6;      /* Main brand blue */
  --primary-purple: #8b5cf6;    /* Secondary brand purple */
  
  /* Functional Colors */
  --success-green: #10b981;     /* Success states, completed tasks */
  --warning-amber: #f59e0b;     /* Warnings, pending items */
  --error-red: #ef4444;         /* Errors, overdue items */
  --info-cyan: #06b6d4;         /* Information, neutral states */
  
  /* Widget-Specific Colors */
  --tasks-orange: #f97316;      /* Today's Tasks widget */
  --exams-rose: #f43f5e;        /* Upcoming Exams widget */
  --progress-emerald: #059669;  /* Chapter Progress widget */
  --swot-violet: #7c3aed;       /* SWOT Analysis widget */
  --leaderboard-indigo: #4f46e5; /* Productivity Leaderboard */
  --streak-teal: #0d9488;       /* Study Streak widget */
  --countdown-pink: #ec4899;    /* D-Day Countdown widget */
  --analytics-slate: #475569;   /* Analytics Overview */
}
```

#### Icon Color Mapping
Each widget type has a distinct icon color to create visual differentiation:

```typescript
const WIDGET_ICON_COLORS = {
  tasks: 'text-orange-500',           // CheckSquare, Clock icons
  exams: 'text-rose-500',             // Calendar, BookOpen icons  
  progress: 'text-emerald-500',       // BarChart3, TrendingUp icons
  swot: 'text-violet-500',            // Brain, Lightbulb icons
  leaderboard: 'text-indigo-500',     // Trophy, Users icons
  streak: 'text-teal-500',            // Flame, Target icons
  countdown: 'text-pink-500',         // Timer, AlertCircle icons
  analytics: 'text-slate-500',        // PieChart, Activity icons
  notifications: 'text-blue-500',     // Bell, Mail icons
  settings: 'text-gray-500',          // Settings, User icons
};
```

### Widget Design Specifications

#### Card Design System
```css
.widget-card {
  @apply bg-white dark:bg-gray-900/50;
  @apply border border-gray-200 dark:border-gray-800;
  @apply rounded-xl;
  @apply backdrop-blur-sm;
  @apply shadow-sm hover:shadow-md;
  @apply transition-all duration-200;
}

.widget-header {
  @apply p-6 pb-4;
  @apply border-b border-gray-100 dark:border-gray-800;
}

.widget-content {
  @apply p-6;
}

.widget-footer {
  @apply px-6 py-4;
  @apply border-t border-gray-100 dark:border-gray-800;
  @apply bg-gray-50/50 dark:bg-gray-800/30;
}
```

#### Individual Widget Designs

##### Today's Tasks Widget
```css
.tasks-widget {
  @apply widget-card;
  @apply border-l-4 border-l-orange-500;
}

.task-item {
  @apply flex items-center gap-3 p-3;
  @apply rounded-lg hover:bg-orange-50 dark:hover:bg-orange-900/10;
  @apply transition-colors duration-150;
}

.task-priority-high {
  @apply border-l-2 border-l-red-400;
}

.task-priority-medium {
  @apply border-l-2 border-l-amber-400;
}

.task-priority-low {
  @apply border-l-2 border-l-green-400;
}
```

##### Upcoming Exams Widget
```css
.exams-widget {
  @apply widget-card;
  @apply border-l-4 border-l-rose-500;
}

.exam-card {
  @apply bg-rose-50 dark:bg-rose-900/10;
  @apply border border-rose-200 dark:border-rose-800;
  @apply rounded-lg p-4;
}

.exam-urgent {
  @apply bg-red-50 dark:bg-red-900/10;
  @apply border-red-200 dark:border-red-800;
  @apply animate-pulse;
}
```

##### Chapter Progress Widget
```css
.progress-widget {
  @apply widget-card;
  @apply border-l-4 border-l-emerald-500;
}

.progress-bar {
  @apply h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-emerald-500 rounded-full;
  @apply transition-all duration-500 ease-out;
}

.difficulty-easy { @apply text-green-500; }
.difficulty-medium { @apply text-amber-500; }
.difficulty-hard { @apply text-red-500; }
```

##### SWOT Analysis Widget
```css
.swot-widget {
  @apply widget-card;
  @apply border-l-4 border-l-violet-500;
}

.swot-grid {
  @apply grid grid-cols-2 gap-4;
}

.swot-quadrant {
  @apply p-4 rounded-lg border-2 border-dashed;
  @apply transition-colors duration-200;
}

.swot-strengths {
  @apply border-green-300 bg-green-50 dark:bg-green-900/10;
  @apply hover:border-green-400;
}

.swot-weaknesses {
  @apply border-red-300 bg-red-50 dark:bg-red-900/10;
  @apply hover:border-red-400;
}

.swot-opportunities {
  @apply border-blue-300 bg-blue-50 dark:bg-blue-900/10;
  @apply hover:border-blue-400;
}

.swot-threats {
  @apply border-amber-300 bg-amber-50 dark:bg-amber-900/10;
  @apply hover:border-amber-400;
}
```

##### Productivity Leaderboard Widget
```css
.leaderboard-widget {
  @apply widget-card;
  @apply border-l-4 border-l-indigo-500;
}

.leaderboard-item {
  @apply flex items-center gap-4 p-3;
  @apply rounded-lg hover:bg-indigo-50 dark:hover:bg-indigo-900/10;
}

.rank-1 { @apply bg-yellow-50 dark:bg-yellow-900/10 border border-yellow-200; }
.rank-2 { @apply bg-gray-50 dark:bg-gray-800/50 border border-gray-200; }
.rank-3 { @apply bg-orange-50 dark:bg-orange-900/10 border border-orange-200; }

.current-user {
  @apply bg-indigo-100 dark:bg-indigo-900/20;
  @apply border-2 border-indigo-300 dark:border-indigo-700;
}
```

##### Study Streak Widget
```css
.streak-widget {
  @apply widget-card;
  @apply border-l-4 border-l-teal-500;
}

.streak-counter {
  @apply text-4xl font-bold text-teal-600 dark:text-teal-400;
  @apply text-center py-6;
}

.streak-calendar {
  @apply grid grid-cols-7 gap-1;
}

.streak-day {
  @apply w-6 h-6 rounded-sm;
  @apply bg-gray-200 dark:bg-gray-700;
}

.streak-day-active {
  @apply bg-teal-500 dark:bg-teal-400;
}

.streak-day-today {
  @apply ring-2 ring-teal-600 dark:ring-teal-300;
}
```

### Icon Library & Usage

#### Primary Icons by Category
```typescript
import {
  // Task Management
  CheckSquare, Clock, AlertCircle, Plus, Edit3,
  
  // Academic/Exams  
  BookOpen, Calendar, GraduationCap, FileText, Award,
  
  // Progress/Analytics
  BarChart3, TrendingUp, PieChart, Activity, Target,
  
  // AI/Intelligence
  Brain, Lightbulb, Zap, Sparkles, Bot,
  
  // Social/Competition
  Trophy, Users, Medal, Crown, Star,
  
  // Time/Streaks
  Flame, Timer, Hourglass, Repeat, Calendar,
  
  // Navigation/UI
  Menu, X, ChevronRight, Search, Bell, Settings,
  
  // Status/Feedback
  CheckCircle, XCircle, AlertTriangle, Info, Help
} from 'lucide-react';

const WIDGET_ICONS = {
  tasks: { primary: CheckSquare, secondary: Clock },
  exams: { primary: Calendar, secondary: BookOpen },
  progress: { primary: BarChart3, secondary: TrendingUp },
  swot: { primary: Brain, secondary: Lightbulb },
  leaderboard: { primary: Trophy, secondary: Users },
  streak: { primary: Flame, secondary: Target },
  countdown: { primary: Timer, secondary: AlertCircle },
  analytics: { primary: PieChart, secondary: Activity }
};
```

### Typography System

```css
.dashboard-title {
  @apply text-3xl font-bold text-gray-900 dark:text-white;
  @apply tracking-tight;
}

.widget-title {
  @apply text-lg font-semibold text-gray-800 dark:text-gray-200;
  @apply flex items-center gap-2;
}

.widget-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
  @apply font-medium;
}

.metric-value {
  @apply text-2xl font-bold;
  @apply tabular-nums; /* For consistent number alignment */
}

.metric-label {
  @apply text-xs uppercase tracking-wide;
  @apply text-gray-500 dark:text-gray-400;
  @apply font-medium;
}
```

### Animation & Interaction Design

#### Micro-interactions
```css
.interactive-element {
  @apply transition-all duration-200 ease-out;
  @apply hover:scale-105 active:scale-95;
}

.widget-hover {
  @apply hover:shadow-lg hover:-translate-y-1;
  @apply transition-all duration-300 ease-out;
}

.button-press {
  @apply active:scale-95;
  @apply transition-transform duration-100;
}

.fade-in {
  @apply animate-in fade-in-0 duration-300;
}

.slide-up {
  @apply animate-in slide-in-from-bottom-4 duration-300;
}
```

#### Loading States
```css
.skeleton {
  @apply bg-gray-200 dark:bg-gray-700;
  @apply animate-pulse rounded;
}

.shimmer {
  @apply relative overflow-hidden;
}

.shimmer::after {
  @apply absolute inset-0;
  @apply bg-gradient-to-r from-transparent via-white/20 to-transparent;
  @apply animate-shimmer;
  content: '';
}
```

### Layout & Spacing System

#### Grid System
```css
.dashboard-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  @apply py-8;
}

.dashboard-grid {
  @apply grid gap-6;
  @apply grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
}

.widget-span-2 {
  @apply md:col-span-2;
}

.widget-span-full {
  @apply col-span-full;
}
```

#### Spacing Scale
```css
:root {
  --space-xs: 0.25rem;    /* 4px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 1rem;       /* 16px */
  --space-lg: 1.5rem;     /* 24px */
  --space-xl: 2rem;       /* 32px */
  --space-2xl: 3rem;      /* 48px */
}
```

### Dark Mode Implementation

#### Theme Variables
```css
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
}
```

#### Widget Theme Adaptations
```css
.widget-card {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.widget-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .widget-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}
```

### Responsive Design

#### Breakpoint Strategy

```css
/* Mobile First Approach */
.dashboard-grid {
  @apply grid grid-cols-1 gap-4;
}

@media (min-width: 768px) {
  .dashboard-grid {
    @apply grid-cols-2 gap-6;
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    @apply grid-cols-3;
  }
}

@media (min-width: 1280px) {
  .dashboard-grid {
    @apply grid-cols-4;
  }
}
```

#### Mobile Optimizations

1. **Touch-Friendly**: Appropriate touch targets (minimum 44px)
2. **Gesture Support**: Swipe actions for mobile interactions
3. **Progressive Enhancement**: Core functionality works without JavaScript
4. **Offline Support**: Cache critical dashboard data for offline viewing

#### Adaptive Layout

- **Widget Stacking**: Intelligent widget stacking on smaller screens
- **Content Prioritization**: Show most important widgets first on mobile
- **Collapsible Sections**: Expandable sections to save screen space
- **Contextual Actions**: Context-aware action buttons based on screen size

### Visual Hierarchy Principles

1. **Size**: Larger elements draw more attention
2. **Color**: Strategic use of color to guide the eye
3. **Contrast**: High contrast for important elements
4. **Spacing**: White space to create breathing room
5. **Typography**: Font weight and size to establish hierarchy
6. **Position**: Important elements in prime locations (top-left, center)

### Accessibility in Design

1. **Color Independence**: Never rely solely on color to convey information
2. **Focus Indicators**: Clear, visible focus states for all interactive elements
3. **Text Contrast**: Minimum 4.5:1 contrast ratio for normal text
4. **Touch Targets**: Minimum 44px for touch interactions
5. **Motion Sensitivity**: Respect user's motion preferences

## Integration with Existing Systems

### Analytics Page Integration

The dashboard widgets integrate seamlessly with the existing comprehensive Analytics page:

```typescript
interface AnalyticsIntegration {
  studyTimeData: {
    source: 'Analytics.tsx';
    widgets: ['StudyStreakWidget', 'AnalyticsOverviewWidget'];
    dataFlow: 'Real-time sync with study sessions';
    caching: 'Local storage + Supabase sync';
  };

  performanceMetrics: {
    source: 'OverviewTab.tsx';
    widgets: ['AnalyticsOverviewWidget', 'StudyStreakWidget'];
    charts: 'Reuse MUI X-Charts components';
    themes: 'Inherit existing light/dark theme system';
  };

  streakCalculation: {
    source: 'Analytics streak logic';
    widget: 'StudyStreakWidget';
    realTimeUpdates: true;
    milestoneTracking: true;
  };
}
```

### Tasks System Integration

Dashboard tasks widget leverages the existing TodoBoard system:

```typescript
interface TasksIntegration {
  dataSource: 'TodoBoard.tsx + Supabase todos table';
  realTimeSync: true;
  dragAndDrop: 'Reuse existing DnD implementation';
  prioritySystem: 'Inherit existing priority colors and logic';
  overdueTracking: 'Enhanced with dashboard-specific alerts';

  quickActions: {
    addTask: 'Simplified modal for dashboard context';
    completeTask: 'Optimistic updates with Supabase sync';
    reorderTasks: 'Maintain existing drag-and-drop functionality';
  };
}
```

### MockTest System Integration

Upcoming exams widget integrates with the comprehensive MockTest system:

```typescript
interface MockTestIntegration {
  dataSource: 'MockTestAnalysis.tsx + Local Storage';
  upcomingTests: 'Filter from enhancedMockTestUtils';
  countdownTimers: 'Real-time countdown with urgency indicators';
  preparationTracking: 'Sync with existing test preparation logic';

  testCompletion: {
    autoMove: 'Move completed tests from upcoming to completed';
    resultPrompt: 'Prompt for test results after completion';
    analyticsSync: 'Update test analytics automatically';
  };

  notifications: {
    examReminders: '24h, 1h, 15min before exam';
    preparationAlerts: 'Low preparation percentage warnings';
    completionPrompts: 'Post-exam result entry reminders';
  };
}
```

### Productivity Timer Integration

Dashboard integrates with the existing Productivity page timer system:

```typescript
interface ProductivityIntegration {
  timerData: 'StudyTimer.tsx + Supabase study sessions';
  streakCalculation: 'Sync with Analytics streak logic';
  pomodoroIntegration: 'Display current session status';

  realTimeUpdates: {
    activeSession: 'Show current timer status in dashboard';
    sessionCompletion: 'Update streak and analytics immediately';
    breakTracking: 'Integrate break analysis data';
  };

  leaderboardData: {
    studyTime: 'Aggregate from all user sessions';
    taskCompletion: 'Sync with TodoBoard completion rates';
    engagement: 'Calculate from app usage patterns';
    ranking: 'Real-time ranking updates';
  };
}
```

## Advanced Widget Specifications

### Prep Insight Board Widget (Comprehensive Study Metrics)

```typescript
interface PrepInsightBoardWidget {
  layout: 'Metrics grid with visual indicators and trend charts';
  dataIntegration: 'Analytics page + Study timer + Streak calculation';

  metricsDisplay: {
    averageDailyStudyTime: {
      value: 'number (minutes)';
      display: 'Hours and minutes format';
      comparison: 'Today vs 7-day average';
      trend: 'Weekly percentage change with arrow indicator';
      color: 'text-blue-600 dark:text-blue-400';
    };
    highestStudyHours: {
      value: 'Personal record in hours';
      display: 'Prominent display with date achieved';
      context: 'Subject or session type if available';
      achievement: 'Golden highlight for records';
      color: 'text-amber-600 dark:text-amber-400';
    };
    currentStudyStreak: {
      value: 'Days in current streak';
      display: 'Large prominent number with flame icon';
      comparison: 'Current vs longest streak';
      milestones: 'Next milestone indicator';
      color: 'text-emerald-600 dark:text-emerald-400';
    };
  };

  visualElements: {
    trendCharts: 'Mini line charts for 7-day study time trends';
    progressRings: 'Circular progress for streak milestones';
    comparisonBars: 'Visual comparison bars for metrics';
    achievementBadges: 'Milestone and record achievement indicators';
  };

  interactions: {
    metricClick: 'Navigate to detailed Analytics page section';
    trendHover: 'Show detailed trend information';
    streakClick: 'Show streak calendar and history';
    recordClick: 'Show personal records and achievements';
  };

  aiInsights: {
    studyPatternAnalysis: 'AI analysis of study consistency and patterns';
    performanceInsights: 'Personalized insights based on metrics';
    recommendations: 'Actionable suggestions for improvement';
    motivationalMessages: 'Context-aware encouragement and tips';
  };
}
```

### SWOT Analysis Widget (AI-Powered)

```typescript
interface SWOTAnalysisWidget {
  layout: 'Quadrant grid with AI insights panel';
  aiIntegration: 'Gemini API for analysis generation';

  quadrants: {
    strengths: {
      color: 'bg-green-50 dark:bg-green-900/10';
      borderColor: 'border-green-300 dark:border-green-700';
      icon: 'TrendingUp';
      placeholder: 'What are you good at in your studies?';
    };
    weaknesses: {
      color: 'bg-red-50 dark:bg-red-900/10';
      borderColor: 'border-red-300 dark:border-red-700';
      icon: 'AlertTriangle';
      placeholder: 'What areas need improvement?';
    };
    opportunities: {
      color: 'bg-blue-50 dark:bg-blue-900/10';
      borderColor: 'border-blue-300 dark:border-blue-700';
      icon: 'Lightbulb';
      placeholder: 'What opportunities can you leverage?';
    };
    threats: {
      color: 'bg-amber-50 dark:bg-amber-900/10';
      borderColor: 'border-amber-300 dark:border-amber-700';
      icon: 'Shield';
      placeholder: 'What challenges might you face?';
    };
  };

  aiInsights: {
    studyMethodsAnalysis: 'AI analysis of study patterns from analytics data';
    gritAssessment: 'Persistence and passion evaluation';
    recommendations: 'Actionable improvement suggestions';
    progressTracking: 'Track SWOT evolution over time';
  };

  interactions: {
    editMode: 'Click to edit any quadrant';
    aiAnalyze: 'Generate AI insights button';
    historyView: 'View SWOT analysis history';
    exportOptions: 'Export as PDF or image';
  };
}
```

### Productivity Leaderboard Widget

```typescript
interface ProductivityLeaderboardWidget {
  layout: 'Top 5 students with current user highlight';
  dataAggregation: 'Study time + task completion + engagement metrics';

  rankingSystem: {
    studyTimeWeight: 40; // 40% of total score
    taskCompletionWeight: 35; // 35% of total score
    engagementWeight: 25; // 25% of total score
    updateFrequency: 'Real-time with 5-minute cache';
  };

  visualDesign: {
    topThree: {
      first: 'Gold crown icon, special highlighting';
      second: 'Silver medal icon, secondary highlighting';
      third: 'Bronze medal icon, tertiary highlighting';
    };
    currentUser: {
      highlighting: 'Distinct border and background color';
      position: 'Always visible regardless of rank';
      animation: 'Subtle glow effect for current user';
    };
  };

  achievements: {
    badges: 'Study streak, task master, consistent learner';
    milestones: 'Weekly, monthly achievement tracking';
    celebrations: 'Rank improvement animations';
  };

  privacy: {
    anonymization: 'Option to show anonymous rankings';
    optOut: 'Users can opt out of leaderboard';
    dataProtection: 'GDPR compliant data handling';
  };
}
```

### Study Streak Widget

```typescript
interface StudyStreakWidget {
  layout: 'Large streak counter with calendar visualization';
  dataSource: 'Analytics page streak calculation logic';

  streakDisplay: {
    currentStreak: {
      typography: 'text-4xl font-bold';
      color: 'text-teal-600 dark:text-teal-400';
      animation: 'Counting animation for updates';
    };
    longestStreak: {
      display: 'Secondary metric below current';
      comparison: 'Progress towards beating longest streak';
    };
  };

  calendarVisualization: {
    layout: 'Grid showing last 30 days';
    dayIndicators: {
      studyDay: 'bg-teal-500 dark:bg-teal-400';
      noStudy: 'bg-gray-200 dark:bg-gray-700';
      today: 'ring-2 ring-teal-600 dark:ring-teal-300';
      future: 'bg-gray-100 dark:bg-gray-800';
    };
    tooltips: 'Show study time on hover';
  };

  milestones: {
    achievements: [7, 14, 30, 60, 100, 365]; // days
    badges: 'Unlock special badges for milestones';
    celebrations: 'Confetti animation for new milestones';
    notifications: 'Milestone achievement notifications';
  };

  motivationalFeatures: {
    encouragement: 'Motivational messages for streak breaks';
    restartHelp: 'Tips for restarting streaks';
    progressInsights: 'Weekly streak pattern analysis';
  };
}
```

### D-Day Countdown Widget

```typescript
interface DDayCountdownWidget {
  layout: 'Large countdown display with goal management';
  multipleGoals: 'Support for multiple D-Day goals with priority';

  countdownDisplay: {
    primary: {
      days: 'Large prominent number';
      hours: 'Secondary display';
      minutes: 'Tertiary display';
      urgencyColors: {
        safe: 'text-green-600'; // >30 days
        warning: 'text-amber-600'; // 7-30 days
        urgent: 'text-red-600'; // <7 days
      };
    };
  };

  goalManagement: {
    addGoal: 'Quick add modal with date picker';
    editGoal: 'Inline editing for goal names and dates';
    deleteGoal: 'Confirmation dialog for goal deletion';
    prioritySystem: 'Drag to reorder goal priority';
  };

  goalTypes: {
    exam: 'Integration with upcoming exams';
    project: 'Custom project deadlines';
    personal: 'Personal goals and milestones';
    academic: 'Academic deadlines and submissions';
  };

  notifications: {
    reminders: 'Configurable reminder notifications';
    milestones: 'Countdown milestone alerts (30d, 7d, 1d)';
    completion: 'Goal completion celebration';
  };

  visualEffects: {
    urgencyAnimation: 'Pulsing effect for urgent goals';
    completionCelebration: 'Confetti animation on goal completion';
    progressRing: 'Circular progress indicator for time elapsed';
  };
}
```

### Chapter Progress Widget

```typescript
interface ChapterProgressWidget {
  layout: 'Subject-grouped chapter list with progress bars';
  dataSource: 'Integration with existing subject system';

  subjectGrouping: {
    collapsibleSections: 'Expandable subject sections';
    subjectColors: 'Inherit existing subject color mapping';
    progressAggregation: 'Overall subject progress calculation';
  };

  chapterDisplay: {
    progressBar: {
      height: 'h-2';
      colors: {
        incomplete: 'bg-gray-200 dark:bg-gray-700';
        inProgress: 'bg-blue-500';
        completed: 'bg-emerald-500';
      };
      animation: 'Smooth progress transitions';
    };

    difficultyIndicators: {
      easy: 'text-green-500 + Easy icon';
      medium: 'text-amber-500 + Medium icon';
      hard: 'text-red-500 + Hard icon';
    };

    metadata: {
      estimatedTime: 'Reading/study time estimates';
      lastAccessed: 'Last accessed timestamp';
      hasNotes: 'Notes availability indicator';
      hasQuiz: 'Quiz availability indicator';
    };
  };

  interactions: {
    chapterClick: 'Navigate to chapter content';
    progressUpdate: 'Manual progress adjustment';
    noteAccess: 'Quick access to chapter notes';
    quizLaunch: 'Launch chapter quiz';
  };

  smartFeatures: {
    recommendedNext: 'AI-suggested next chapter to study';
    timeEstimates: 'Personalized time estimates based on history';
    difficultyAdjustment: 'Dynamic difficulty based on performance';
  };
}
```

### Analytics Overview Widget

```typescript
interface AnalyticsOverviewWidget {
  layout: 'Key metrics cards with trend indicators';
  dataSource: 'Analytics.tsx comprehensive data';

  keyMetrics: {
    studyTime: {
      today: 'Hours studied today';
      thisWeek: 'Weekly study time';
      trend: 'Week-over-week comparison';
      target: 'Progress towards daily target';
    };

    taskCompletion: {
      completed: 'Tasks completed today';
      total: 'Total tasks for today';
      completionRate: 'Percentage completion';
      streak: 'Task completion streak';
    };

    examPreparation: {
      upcomingCount: 'Number of upcoming exams';
      averagePreparation: 'Average preparation percentage';
      urgentCount: 'Exams requiring immediate attention';
    };

    subjectProgress: {
      totalChapters: 'Total chapters across subjects';
      completedChapters: 'Completed chapters count';
      averageProgress: 'Overall progress percentage';
    };
  };

  visualizations: {
    trendCharts: 'Mini line charts for trends';
    progressRings: 'Circular progress indicators';
    comparisonBars: 'Week-over-week comparison bars';
    heatmaps: 'Study pattern heatmaps';
  };

  drillDownCapabilities: {
    studyTimeDetails: 'Navigate to Analytics daily view';
    taskDetails: 'Navigate to Tasks page';
    examDetails: 'Navigate to MockTest page';
    subjectDetails: 'Navigate to subject-specific analytics';
  };
}
```

## Mobile-First Responsive Design

### Breakpoint Strategy

```css
/* Mobile-first responsive design */
.dashboard-container {
  /* Mobile (default) */
  @apply px-4 py-6;

  /* Tablet */
  @media (min-width: 768px) {
    @apply px-6 py-8;
  }

  /* Desktop */
  @media (min-width: 1024px) {
    @apply px-8 py-12;
  }

  /* Large Desktop */
  @media (min-width: 1280px) {
    @apply px-12 py-16;
  }
}

.dashboard-grid {
  /* Mobile: Single column */
  @apply grid grid-cols-1 gap-4;

  /* Tablet: Two columns */
  @media (min-width: 768px) {
    @apply grid-cols-2 gap-6;
  }

  /* Desktop: Three columns */
  @media (min-width: 1024px) {
    @apply grid-cols-3;
  }

  /* Large Desktop: Four columns */
  @media (min-width: 1280px) {
    @apply grid-cols-4;
  }
}
```

### Mobile Widget Adaptations

```typescript
interface MobileWidgetAdaptations {
  todaysTasks: {
    mobile: 'Compact task list with swipe actions';
    tablet: 'Standard layout with drag-and-drop';
    desktop: 'Full featured with all interactions';
  };

  upcomingExams: {
    mobile: 'Card carousel with horizontal scroll';
    tablet: 'Grid layout with 2 columns';
    desktop: 'Full grid with detailed information';
  };

  swotAnalysis: {
    mobile: 'Tabbed interface for quadrants';
    tablet: '2x2 grid with reduced padding';
    desktop: 'Full quadrant grid with AI panel';
  };

  leaderboard: {
    mobile: 'Vertical list with minimal info';
    tablet: 'Enhanced list with avatars';
    desktop: 'Full featured with achievements';
  };

  studyStreak: {
    mobile: 'Large counter with mini calendar';
    tablet: 'Counter with expanded calendar';
    desktop: 'Full calendar with detailed tooltips';
  };
}
```

### Touch Interactions

```css
.touch-target {
  @apply min-h-[44px] min-w-[44px]; /* WCAG touch target minimum */
}

.swipe-action {
  @apply touch-pan-x; /* Enable horizontal swipe */
}

.drag-handle {
  @apply touch-none; /* Disable touch for drag handles on mobile */
}

/* Mobile-specific hover states */
@media (hover: none) {
  .hover-effect {
    @apply active:scale-95; /* Use active instead of hover */
  }
}
```

## Performance Optimization Strategies

### Lazy Loading Implementation

```typescript
interface LazyLoadingStrategy {
  widgetLazyLoading: {
    implementation: 'React.lazy() for each widget component';
    trigger: 'Intersection Observer for viewport visibility';
    fallback: 'Skeleton loading states';
    preloading: 'Preload next widgets on user interaction';
  };

  dataLazyLoading: {
    analytics: 'Load analytics data only when widget is visible';
    mockTests: 'Lazy load test data with pagination';
    tasks: 'Load tasks incrementally';
    leaderboard: 'Cache with 5-minute refresh interval';
  };

  imageLazyLoading: {
    avatars: 'Lazy load user avatars with placeholder';
    charts: 'Load chart libraries only when needed';
    icons: 'Use icon fonts for better performance';
  };
}
```

### Caching Strategy

```typescript
interface CachingStrategy {
  localStorageCache: {
    userPreferences: 'Dashboard layout and widget settings';
    recentData: 'Cache recent analytics and task data';
    offlineSupport: 'Essential data for offline viewing';
  };

  supabaseCache: {
    realTimeSubscriptions: 'Efficient real-time data updates';
    queryOptimization: 'Optimized database queries';
    batchOperations: 'Batch multiple operations for efficiency';
  };

  reactQueryCache: {
    staleTime: '5 minutes for analytics data';
    cacheTime: '30 minutes for user data';
    backgroundRefetch: 'Update data in background';
  };
}
```

### Bundle Optimization

```typescript
interface BundleOptimization {
  codesplitting: {
    routeLevel: 'Split dashboard from other pages';
    componentLevel: 'Split each widget into separate chunks';
    libraryLevel: 'Split heavy libraries (charts, animations)';
  };

  treeshaking: {
    lucideIcons: 'Import only used icons';
    lodash: 'Import specific utility functions';
    framerMotion: 'Import only required animation components';
  };

  assetOptimization: {
    images: 'WebP format with fallbacks';
    fonts: 'Subset fonts for used characters';
    css: 'Purge unused CSS classes';
  };
}
```